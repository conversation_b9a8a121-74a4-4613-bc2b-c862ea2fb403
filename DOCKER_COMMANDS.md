# Docker Commands для Parttec

## Основные команды

### Запуск и остановка

```bash
# Запуск всех сервисов (development)
docker-compose up -d

# Запуск с пересборкой
docker-compose up -d --build

# Запуск production версии
docker-compose -f docker-compose.prod.yml up -d --build

# Остановка всех сервисов
docker-compose down

# Остановка с удалением volumes (ОСТОРОЖНО!)
docker-compose down -v
```

### Просмотр статуса и логов

```bash
# Статус всех контейнеров
docker-compose ps

# Логи всех сервисов
docker-compose logs -f

# Логи конкретного сервиса
docker-compose logs -f api
docker-compose logs -f cpanel
docker-compose logs -f site
docker-compose logs -f postgres
docker-compose logs -f redis

# Последние 100 строк логов
docker-compose logs --tail=100 api
```

## Работа с базой данных

### Подключение к PostgreSQL

```bash
# Подключение к PostgreSQL через psql
docker exec -it parttec-postgres psql -U postgres -d parttec

# Выполнение SQL команды
docker exec parttec-postgres psql -U postgres -d parttec -c "SELECT COUNT(*) FROM users;"

# Создание бэкапа
docker exec parttec-postgres pg_dump -U postgres parttec > backup_$(date +%Y%m%d_%H%M%S).sql

# Восстановление из бэкапа
docker exec -i parttec-postgres psql -U postgres parttec < backup.sql
```

### Prisma команды

```bash
# Войти в контейнер API
docker exec -it parttec-api bash

# Внутри контейнера API:
bunx prisma migrate deploy    # Применить миграции
bunx prisma generate         # Сгенерировать клиент
bunx prisma db push          # Синхронизировать схему
bunx prisma studio           # Открыть Prisma Studio (не работает в контейнере)

# Выполнить команду без входа в контейнер
docker exec parttec-api bunx prisma migrate deploy
docker exec parttec-api bunx prisma generate
```

## Работа с Redis

```bash
# Подключение к Redis CLI
docker exec -it parttec-redis redis-cli

# Проверка подключения
docker exec parttec-redis redis-cli ping

# Просмотр всех ключей
docker exec parttec-redis redis-cli keys "*"

# Очистка всех данных Redis
docker exec parttec-redis redis-cli flushall
```

## Отладка и диагностика

### Проверка ресурсов

```bash
# Использование ресурсов контейнерами
docker stats

# Информация о конкретном контейнере
docker inspect parttec-api

# Процессы внутри контейнера
docker exec parttec-api ps aux
```

### Работа с файлами

```bash
# Копирование файлов из контейнера
docker cp parttec-api:/app/uploads ./uploads-backup

# Копирование файлов в контейнер
docker cp ./local-file.txt parttec-api:/app/

# Просмотр файловой системы контейнера
docker exec parttec-api ls -la /app
docker exec parttec-api du -sh /app/*
```

### Сетевая диагностика

```bash
# Проверка сетевых подключений
docker network ls
docker network inspect parttec_parttec-network

# Проверка портов
docker port parttec-api
docker port parttec-cpanel
docker port parttec-site

# Тест подключения между контейнерами
docker exec parttec-api ping postgres
docker exec parttec-api ping redis
```

## Очистка и обслуживание

### Очистка Docker

```bash
# Удаление неиспользуемых образов
docker image prune

# Удаление неиспользуемых контейнеров
docker container prune

# Удаление неиспользуемых volumes
docker volume prune

# Полная очистка системы (ОСТОРОЖНО!)
docker system prune -a

# Очистка с удалением volumes
docker system prune -a --volumes
```

### Пересборка конкретного сервиса

```bash
# Пересборка только API
docker-compose build --no-cache api
docker-compose up -d api

# Пересборка только CPPanel
docker-compose build --no-cache cpanel
docker-compose up -d cpanel

# Пересборка только Site
docker-compose build --no-cache site
docker-compose up -d site
```

## Мониторинг производительности

### Мониторинг ресурсов

```bash
# Реальное время использования ресурсов
docker stats --no-stream

# Использование дискового пространства
docker system df

# Размер образов
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### Health Checks

```bash
# Проверка health status (только для production compose)
docker-compose -f docker-compose.prod.yml ps

# Ручная проверка health endpoints
curl -f http://localhost:3000/api/auth/session
curl -f http://localhost:4322/
curl -f http://localhost:4323/
```

## Полезные алиасы

Добавьте в ваш `.bashrc` или `.zshrc`:

```bash
# Алиасы для Parttec Docker
alias parttec-up='docker-compose up -d'
alias parttec-down='docker-compose down'
alias parttec-logs='docker-compose logs -f'
alias parttec-ps='docker-compose ps'
alias parttec-api='docker exec -it parttec-api bash'
alias parttec-db='docker exec -it parttec-postgres psql -U postgres -d parttec'
alias parttec-redis='docker exec -it parttec-redis redis-cli'

# Production версии
alias parttec-prod-up='docker-compose -f docker-compose.prod.yml up -d'
alias parttec-prod-down='docker-compose -f docker-compose.prod.yml down'
alias parttec-prod-logs='docker-compose -f docker-compose.prod.yml logs -f'
```

## Экстренные команды

### Аварийная остановка

```bash
# Принудительная остановка всех контейнеров
docker kill $(docker ps -q)

# Удаление всех контейнеров
docker rm -f $(docker ps -aq)
```

### Восстановление после сбоя

```bash
# Полный перезапуск
docker-compose down
docker-compose up -d --force-recreate

# Пересоздание volumes (потеря данных!)
docker-compose down -v
docker volume rm parttec_postgres_data parttec_redis_data parttec_api_uploads
docker-compose up -d
```
