@echo off
setlocal enabledelayedexpansion

REM Скрипт для деплоя Parttec монорепо (Windows)

REM Проверка наличия Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker не установлен. Установите Docker и попробуйте снова.
    exit /b 1
)

REM Проверка наличия Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose не установлен. Установите Docker Compose и попробуйте снова.
    exit /b 1
)

REM Проверка наличия .env файла
if not exist .env (
    echo [WARN] .env файл не найден. Создаю из .env.example...
    if exist .env.example (
        copy .env.example .env
        echo [WARN] Отредактируйте .env файл перед продолжением!
        exit /b 1
    ) else (
        echo [ERROR] .env.example файл не найден!
        exit /b 1
    )
)

if "%1"=="dev" goto deploy_dev
if "%1"=="prod" goto deploy_prod
if "%1"=="stop" goto stop
if "%1"=="logs" goto logs
if "%1"=="clean" goto clean
goto usage

:deploy_dev
echo [INFO] Запуск в development режиме...

echo [INFO] Создание директорий для персистентных данных...
if not exist data mkdir data
if not exist data\postgres mkdir data\postgres
if not exist data\uploads mkdir data\uploads

echo [INFO] Обновление подмодулей...
git submodule update --init --recursive

echo [INFO] Остановка существующих контейнеров...
docker-compose down

echo [INFO] Сборка и запуск контейнеров...
docker-compose up -d --build

echo [INFO] Ожидание запуска PostgreSQL...
timeout /t 10 /nobreak >nul

echo [INFO] Выполнение миграций базы данных...
docker exec parttec-api bunx prisma migrate deploy

echo [INFO] Development деплой завершен!
echo [INFO] Сервисы доступны по адресам:
echo [INFO]   API: http://localhost:3000
echo [INFO]   CPPanel: http://localhost:4322
echo [INFO]   Site: http://localhost:4323
goto end

:deploy_prod
echo [INFO] Запуск в production режиме...

findstr /C:"API_DOMAIN" .env >nul
if errorlevel 1 (
    echo [ERROR] Не найдены production переменные в .env файле!
    echo [ERROR] Добавьте API_DOMAIN, CPANEL_DOMAIN, SITE_DOMAIN
    exit /b 1
)

echo [INFO] Создание директорий для персистентных данных...
if not exist data mkdir data
if not exist data\postgres mkdir data\postgres
if not exist data\uploads mkdir data\uploads

echo [INFO] Обновление подмодулей...
git submodule update --init --recursive

echo [INFO] Остановка существующих контейнеров...
docker-compose -f docker-compose.prod.yml down

echo [INFO] Сборка и запуск production контейнеров...
docker-compose -f docker-compose.prod.yml up -d --build

echo [INFO] Ожидание запуска PostgreSQL...
timeout /t 15 /nobreak >nul

echo [INFO] Выполнение миграций базы данных...
docker exec parttec-api bunx prisma migrate deploy

echo [INFO] Production деплой завершен!
goto end

:stop
echo [INFO] Остановка всех контейнеров...
docker-compose down
docker-compose -f docker-compose.prod.yml down
echo [INFO] Контейнеры остановлены.
goto end

:logs
if "%2"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %2
)
goto end

:clean
echo [WARN] Это удалит все контейнеры, образы и volumes!
set /p confirm="Вы уверены? (y/N): "
if /i "!confirm!"=="y" (
    echo [INFO] Очистка Docker ресурсов...
    docker-compose down -v
    docker-compose -f docker-compose.prod.yml down -v
    docker system prune -a -f
    echo [INFO] Очистка завершена.
) else (
    echo [INFO] Очистка отменена.
)
goto end

:usage
echo Использование: %0 {dev^|prod^|stop^|logs [service]^|clean}
echo.
echo Команды:
echo   dev     - Запуск в development режиме
echo   prod    - Запуск в production режиме
echo   stop    - Остановка всех контейнеров
echo   logs    - Просмотр логов (опционально указать сервис)
echo   clean   - Полная очистка Docker ресурсов
echo.
echo Примеры:
echo   %0 dev
echo   %0 logs api
echo   %0 stop
exit /b 1

:end
