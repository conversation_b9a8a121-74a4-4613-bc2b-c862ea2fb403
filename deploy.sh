#!/bin/bash

# Скрипт для деплоя Parttec монорепо

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функция для вывода сообщений
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка наличия Docker
if ! command -v docker &> /dev/null; then
    error "Docker не установлен. Установите Docker и попробуйте снова."
    exit 1
fi

# Проверка наличия Docker Compose
if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose не установлен. Установите Docker Compose и попробуйте снова."
    exit 1
fi

# Проверка наличия .env файла
if [ ! -f .env ]; then
    warn ".env файл не найден. Создаю из .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        warn "Отредактируйте .env файл перед продолжением!"
        exit 1
    else
        error ".env.example файл не найден!"
        exit 1
    fi
fi

# Функция для development деплоя
deploy_dev() {
    log "Запуск в development режиме..."

    # Создаем директории для данных
    log "Создание директорий для персистентных данных..."
    mkdir -p data/postgres data/uploads

    # Обновляем подмодули
    log "Обновление подмодулей..."
    git submodule update --init --recursive
    
    # Останавливаем существующие контейнеры
    log "Остановка существующих контейнеров..."
    docker-compose down
    
    # Собираем и запускаем
    log "Сборка и запуск контейнеров..."
    docker-compose up -d --build
    
    # Ждем запуска PostgreSQL
    log "Ожидание запуска PostgreSQL..."
    sleep 10
    
    # Выполняем миграции
    log "Выполнение миграций базы данных..."
    docker exec parttec-api bunx prisma migrate deploy || warn "Миграции не выполнены. Возможно, база данных уже настроена."
    
    log "Development деплой завершен!"
    log "Сервисы доступны по адресам:"
    log "  API: http://localhost:3000"
    log "  CPPanel: http://localhost:4322"
    log "  Site: http://localhost:4323"
}

# Функция для production деплоя
deploy_prod() {
    log "Запуск в production режиме..."

    # Проверяем наличие production переменных
    if ! grep -q "API_DOMAIN" .env; then
        error "Не найдены production переменные в .env файле!"
        error "Добавьте API_DOMAIN, CPANEL_DOMAIN, SITE_DOMAIN"
        exit 1
    fi

    # Создаем директории для данных
    log "Создание директорий для персистентных данных..."
    mkdir -p data/postgres data/uploads

    # Обновляем подмодули
    log "Обновление подмодулей..."
    git submodule update --init --recursive
    
    # Останавливаем существующие контейнеры
    log "Остановка существующих контейнеров..."
    docker-compose -f docker-compose.prod.yml down
    
    # Собираем и запускаем
    log "Сборка и запуск production контейнеров..."
    docker-compose -f docker-compose.prod.yml up -d --build
    
    # Ждем запуска PostgreSQL
    log "Ожидание запуска PostgreSQL..."
    sleep 15
    
    # Выполняем миграции
    log "Выполнение миграций базы данных..."
    docker exec parttec-api bunx prisma migrate deploy || warn "Миграции не выполнены. Возможно, база данных уже настроена."
    
    log "Production деплой завершен!"
}

# Функция для остановки
stop() {
    log "Остановка всех контейнеров..."
    docker-compose down
    docker-compose -f docker-compose.prod.yml down
    log "Контейнеры остановлены."
}

# Функция для просмотра логов
logs() {
    if [ -z "$1" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$1"
    fi
}

# Функция для очистки
clean() {
    warn "Это удалит все контейнеры, образы и volumes!"
    read -p "Вы уверены? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Очистка Docker ресурсов..."
        docker-compose down -v
        docker-compose -f docker-compose.prod.yml down -v
        docker system prune -a -f
        log "Очистка завершена."
    else
        log "Очистка отменена."
    fi
}

# Основная логика
case "$1" in
    "dev")
        deploy_dev
        ;;
    "prod")
        deploy_prod
        ;;
    "stop")
        stop
        ;;
    "logs")
        logs "$2"
        ;;
    "clean")
        clean
        ;;
    *)
        echo "Использование: $0 {dev|prod|stop|logs [service]|clean}"
        echo ""
        echo "Команды:"
        echo "  dev     - Запуск в development режиме"
        echo "  prod    - Запуск в production режиме"
        echo "  stop    - Остановка всех контейнеров"
        echo "  logs    - Просмотр логов (опционально указать сервис)"
        echo "  clean   - Полная очистка Docker ресурсов"
        echo ""
        echo "Примеры:"
        echo "  $0 dev"
        echo "  $0 logs api"
        echo "  $0 stop"
        exit 1
        ;;
esac
