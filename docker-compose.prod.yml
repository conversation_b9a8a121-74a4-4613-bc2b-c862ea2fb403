version: '3.8'

services:
  # База данных PostgreSQL
  postgres:
    image: postgres:16-alpine
    container_name: parttec-postgres
    environment:
      POSTGRES_DB: parttec
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis для кэширования и сессий
  redis:
    image: redis:7-alpine
    container_name: parttec-redis
    volumes:
      - redis_data:/data
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API сервис (Bun + Hono + tRPC)
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: parttec-api
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/parttec
      - REDIS_URL=redis://redis:6379
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${API_URL}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - api_uploads:/app/uploads
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/auth/session"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`${API_DOMAIN}`)"
      - "traefik.http.routers.api.tls=true"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=3000"

  # CPPanel (Astro + Vue)
  cpanel:
    build:
      context: ./cpanel
      dockerfile: Dockerfile
    container_name: parttec-cpanel
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL}
    depends_on:
      api:
        condition: service_healthy
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4322"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cpanel.rule=Host(`${CPANEL_DOMAIN}`)"
      - "traefik.http.routers.cpanel.tls=true"
      - "traefik.http.routers.cpanel.tls.certresolver=letsencrypt"
      - "traefik.http.services.cpanel.loadbalancer.server.port=4322"

  # Site (Astro + React)
  site:
    build:
      context: ./site
      dockerfile: Dockerfile
    container_name: parttec-site
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL}
    depends_on:
      api:
        condition: service_healthy
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4323"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.site.rule=Host(`${SITE_DOMAIN}`)"
      - "traefik.http.routers.site.tls=true"
      - "traefik.http.routers.site.tls.certresolver=letsencrypt"
      - "traefik.http.services.site.loadbalancer.server.port=4323"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_uploads:
    driver: local

networks:
  parttec-network:
    driver: bridge
